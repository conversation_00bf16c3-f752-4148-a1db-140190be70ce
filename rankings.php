<?php
require_once __DIR__.'/app/auth.php';
start_session();

// Get basic config
$config = load_config();
$websiteTitle = $config['website_title'] ?? 'Elyon Aion';
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Rankings - <?php echo htmlspecialchars($websiteTitle); ?></title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --aion-gold: #ffd700;
            --aion-blue: #4a90e2;
            --aion-red: #e74c3c;
            --aion-dark: #0a0e1a;
            --aion-darker: #050810;
        }

        body {
            background-color: #000;
            color: #e9e6dd;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Epic Aion Background (from usercp) */
        .page-bg {
            position: fixed;
            inset: 0;
            z-index: -1;
            pointer-events: none;
            background:
                radial-gradient(circle at 20% 80%, rgba(204,184,109,0.15) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(74,144,226,0.1) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(231,76,60,0.08) 0%, transparent 60%),
                linear-gradient(45deg, rgba(0,0,0,0.9) 0%, rgba(20,20,40,0.8) 50%, rgba(0,0,0,0.9) 100%),
                url('images/bg_atreia.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            filter: brightness(0.6) contrast(1.2) saturate(1.1);
            animation: backgroundPulse 25s ease-in-out infinite;
        }

        /* Floating Particles Effect */
        .page-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255,215,0,0.8), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(74,144,226,0.6), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(231,76,60,0.5), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.4), transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(204,184,109,0.7), transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: floatingParticles 30s linear infinite;
            opacity: 0.6;
        }

        /* Magical Aura Effect */
        .page-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 20%, rgba(255,215,0,0.1), transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(74,144,226,0.08), transparent 50%),
                radial-gradient(circle at 20% 60%, rgba(231,76,60,0.06), transparent 50%);
            animation: magicalAura 20s ease-in-out infinite;
            opacity: 0.8;
        }

        @keyframes backgroundPulse {
            0%, 100% { filter: brightness(0.6) contrast(1.2) saturate(1.1); }
            50% { filter: brightness(0.7) contrast(1.3) saturate(1.2); }
        }

        @keyframes floatingParticles {
            0% { transform: translateY(0px) translateX(0px); }
            33% { transform: translateY(-100px) translateX(50px); }
            66% { transform: translateY(-200px) translateX(-30px); }
            100% { transform: translateY(-300px) translateX(20px); }
        }

        @keyframes magicalAura {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        /* Epic floating particles */
        .epic-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .epic-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--aion-gold);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
            box-shadow: 0 0 10px var(--aion-gold);
        }

        .epic-particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .epic-particle:nth-child(2) { left: 20%; animation-delay: 1s; }
        .epic-particle:nth-child(3) { left: 30%; animation-delay: 2s; }
        .epic-particle:nth-child(4) { left: 40%; animation-delay: 3s; }
        .epic-particle:nth-child(5) { left: 50%; animation-delay: 4s; }
        .epic-particle:nth-child(6) { left: 60%; animation-delay: 5s; }
        .epic-particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
        .epic-particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
        .epic-particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

        @keyframes float {
            0%, 100% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        /* Epic header */
        .epic-header {
            background:
                linear-gradient(145deg, rgba(255,215,0,0.2), rgba(255,215,0,0.05)),
                radial-gradient(circle at center, rgba(255,215,0,0.1), transparent 70%);
            border: 3px solid;
            border-image: linear-gradient(45deg, var(--aion-gold), var(--aion-blue), var(--aion-red), var(--aion-gold)) 1;
            border-radius: 25px;
            padding: 3rem;
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: headerPulse 4s ease-in-out infinite;
            backdrop-filter: blur(10px);
        }

        .epic-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255,215,0,0.3), transparent);
            animation: rotate 8s linear infinite;
            z-index: -1;
        }

        .epic-header h1 {
            color: var(--aion-gold);
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow:
                0 0 30px rgba(255,215,0,0.8),
                0 0 60px rgba(255,215,0,0.6),
                0 4px 8px rgba(0,0,0,0.8);
            animation: titleGlow 3s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }

        .epic-header p {
            color: #e9e6dd;
            font-size: 1.3rem;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0,0,0,0.8);
            position: relative;
            z-index: 2;
        }

        @keyframes headerPulse {
            0%, 100% { box-shadow: 0 0 30px rgba(255,215,0,0.3); }
            50% { box-shadow: 0 0 60px rgba(255,215,0,0.6), 0 0 100px rgba(255,215,0,0.3); }
        }

        @keyframes titleGlow {
            0%, 100% { text-shadow: 0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6), 0 4px 8px rgba(0,0,0,0.8); }
            50% { text-shadow: 0 0 50px rgba(255,215,0,1), 0 0 100px rgba(255,215,0,0.8), 0 4px 8px rgba(0,0,0,0.8); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Epic ranking cards */
        .epic-card {
            background:
                linear-gradient(145deg, rgba(10,10,30,0.95), rgba(5,5,20,0.98)),
                radial-gradient(circle at top, rgba(255,215,0,0.1), transparent 60%);
            border: 2px solid;
            border-image: linear-gradient(45deg, var(--aion-gold), var(--aion-blue), var(--aion-gold)) 1;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: cardFloat 6s ease-in-out infinite;
        }

        .epic-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.6),
                0 0 50px rgba(255,215,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .epic-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,215,0,0.2), transparent);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .epic-card h5 {
            color: var(--aion-gold);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 20px rgba(255,215,0,0.6);
            position: relative;
            z-index: 2;
        }

        .epic-card h6 {
            color: var(--aion-gold);
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-shadow: 0 0 15px rgba(255,215,0,0.5);
        }

        /* Epic player entries */
        .epic-player {
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border: 1px solid rgba(255,215,0,0.3);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
        }

        .epic-player:hover {
            transform: translateX(10px) scale(1.02);
            border-color: var(--aion-gold);
            box-shadow:
                0 8px 25px rgba(0,0,0,0.4),
                0 0 30px rgba(255,215,0,0.3);
        }

        .epic-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,215,0,0.1), transparent);
            transition: left 0.5s ease;
        }

        .epic-player:hover::before {
            left: 100%;
        }

        .player-rank {
            font-size: 1.5rem;
            font-weight: 900;
            color: var(--aion-gold);
            text-shadow: 0 0 15px rgba(255,215,0,0.8);
            margin-right: 1rem;
            min-width: 3rem;
            text-align: center;
        }

        .player-name-epic {
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.8);
        }

        .player-name-epic.elyos {
            color: var(--aion-blue);
            text-shadow: 0 0 15px rgba(74,144,226,0.6), 0 2px 4px rgba(0,0,0,0.8);
        }

        .player-name-epic.asmodian {
            color: var(--aion-red);
            text-shadow: 0 0 15px rgba(231,76,60,0.6), 0 2px 4px rgba(0,0,0,0.8);
        }

        .player-stats {
            color: #ccc;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .race-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .race-badge.elyos {
            background: linear-gradient(135deg, var(--aion-blue), #357abd);
            color: white;
            box-shadow: 0 0 15px rgba(74,144,226,0.4);
        }

        .race-badge.asmodian {
            background: linear-gradient(135deg, var(--aion-red), #c0392b);
            color: white;
            box-shadow: 0 0 15px rgba(231,76,60,0.4);
        }

        /* Epic animations */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <!-- Epic Aion Background -->
    <div class="page-bg"></div>

    <!-- Epic Floating Particles -->
    <div class="epic-particles">
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
        <div class="epic-particle"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-dark" style="background: linear-gradient(135deg, rgba(10,10,30,0.95), rgba(5,5,20,0.98)); backdrop-filter: blur(10px);">
        <div class="container">
            <span class="navbar-brand" style="color: var(--aion-gold); font-weight: 700; text-shadow: 0 0 20px rgba(255,215,0,0.6);">
                <?php echo htmlspecialchars($websiteTitle); ?>
            </span>
            <div class="d-flex">
                <a class="btn btn-outline-warning me-2" href="index.php" style="border-color: var(--aion-gold); color: var(--aion-gold);">Home</a>
                <?php if(isLoggedIn()): ?>
                    <a class="btn btn-outline-info me-2" href="usercp.php">Dashboard</a>
                    <a class="btn btn-warning" href="logout.php">Logout</a>
                <?php else: ?>
                    <a class="btn btn-primary" href="login.php">Login</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5" style="position: relative; z-index: 2;">
        <!-- Epic Header -->
        <div class="epic-header">
            <h1><i class="fas fa-trophy me-3" style="animation: bounce 2s ease-in-out infinite;"></i>LEGENDARY RANKINGS</h1>
            <p>⚔️ Witness the most ELITE warriors and champions of <?php echo htmlspecialchars($websiteTitle); ?> ⚔️</p>
        </div>

        <!-- Epic PvP Rankings -->
        <div class="epic-card" style="animation-delay: 0.0s;">
            <h5><i class="fas fa-skull-crossbones me-3" style="color: #dc3545; animation: pulse 2s ease-in-out infinite;"></i>⚔️ PVP DOMINANCE ⚔️</h5>
            <div class="row">
                <div class="col-lg-6">
                    <h6><i class="fas fa-eye me-2" style="color: #6f42c1;"></i>👁️ ABYSS LORDS</h6>
                    <div id="top-abyss-players" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Calculating Abyss Points...
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h6><i class="fas fa-star me-2" style="color: #ffc107;"></i>⭐ GLORY CHAMPIONS</h6>
                    <div id="top-gp-players" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Counting Glory Points...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Epic Hall of Fame -->
        <div class="epic-card" style="animation-delay: 0.2s;">
            <h5><i class="fas fa-crown me-3" style="color: var(--aion-gold); animation: pulse 2s ease-in-out infinite;"></i>🏛️ HALL OF LEGENDS 🏛️</h5>
            <div class="row">
                <div class="col-lg-6">
                    <h6><i class="fas fa-level-up-alt me-2"></i>🏆 APEX WARRIORS</h6>
                    <div id="top-level-players" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Summoning Champions...
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h6><i class="fas fa-sword me-2"></i>⚔️ PVP OVERLORDS</h6>
                    <div id="top-pvp-players" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Gathering Legends...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Epic Additional Rankings -->
        <div class="epic-card" style="animation-delay: 0.4s;">
            <h5><i class="fas fa-medal me-3" style="color: var(--aion-gold); animation: rotate 4s linear infinite;"></i>🌟 ELITE RANKINGS 🌟</h5>
            <div class="row">
                <div class="col-lg-4">
                    <h6><i class="fas fa-coins me-2" style="color: #ffd700;"></i>💰 WEALTH TITANS</h6>
                    <div id="top-wealth-players" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Counting Kinah...
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <h6><i class="fas fa-shield-alt me-2" style="color: #4a90e2;"></i>🏰 GUILD SUPREMACY</h6>
                    <div id="top-guilds" class="mb-3">
                        <div class="text-center" style="color: var(--aion-gold); animation: pulse 1.5s ease-in-out infinite;">
                            <i class="fas fa-spinner fa-spin me-2"></i>Assembling Legions...
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load ranking data
        async function loadRankings() {
            try {
                console.log('Fetching server stats...');
                const response = await fetch('server-stats.php', { cache: 'no-store' });
                if (!response.ok) throw new Error('Failed to fetch stats');

                const raw = await response.text();
                console.log('Raw server-stats.php:', raw);
                let stats;
                try { stats = JSON.parse(raw); } catch (e) { throw new Error('Invalid JSON from server-stats.php'); }
                console.log('Server stats received:', stats);

                // Update top level players
                const topLevelContainer = document.getElementById('top-level-players');
                topLevelContainer.innerHTML = '';

                if (!stats.top_level_players || stats.top_level_players.length === 0) {
                    topLevelContainer.innerHTML = '<div class="text-center text-muted">No legendary warriors found</div>';
                } else {
                    let html = '';
                    stats.top_level_players.forEach((player, index) => {
                        const raceClass = player.race.toLowerCase();
                        const rankIcon = index === 0 ? '👑' : index === 1 ? '🥈' : index === 2 ? '🥉' : '⭐';
                        html += `
                            <div class="epic-player" style="animation-delay: ${index * 0.1}s;">
                                <div class="d-flex align-items-center">
                                    <div class="player-rank">${rankIcon} #${index + 1}</div>
                                    <div class="flex-grow-1">
                                        <div class="player-name-epic ${raceClass}">${player.name}</div>
                                        <div class="player-stats">Level ${player.level} • ${player.class}</div>
                                    </div>
                                    <span class="race-badge ${raceClass}">${player.race}</span>
                                </div>
                            </div>
                        `;
                    });
                    topLevelContainer.innerHTML = html;
                }

                // Update top PvP players
                const topPvpContainer = document.getElementById('top-pvp-players');
                topPvpContainer.innerHTML = '';

                if (!stats.top_pvp_players || stats.top_pvp_players.length === 0) {
                    topPvpContainer.innerHTML = '<div class="text-center text-muted">No PvP overlords found</div>';
                } else {
                    let html = '';
                    stats.top_pvp_players.forEach((player, index) => {
                        const raceClass = player.race.toLowerCase();
                        const rankIcon = index === 0 ? '⚔️' : index === 1 ? '🗡️' : index === 2 ? '🏹' : '💀';
                        html += `
                            <div class="epic-player" style="animation-delay: ${index * 0.1 + 0.5}s;">
                                <div class="d-flex align-items-center">
                                    <div class="player-rank">${rankIcon} #${index + 1}</div>
                                    <div class="flex-grow-1">
                                        <div class="player-name-epic ${raceClass}">${player.name}</div>
                                        <div class="player-stats">${player.kills} kills • ${player.class}</div>
                                    </div>
                                    <span class="race-badge ${raceClass}">${player.race}</span>
                                </div>
                            </div>
                        `;
                    });
                    topPvpContainer.innerHTML = html;
                }

                // Update wealth players
                const topWealthContainer = document.getElementById('top-wealth-players');
                topWealthContainer.innerHTML = '';

                if (!stats.top_wealth_players || stats.top_wealth_players.length === 0) {
                    topWealthContainer.innerHTML = '<div class="text-center text-muted">No wealth data available</div>';
                } else {
                    let html = '';
                    stats.top_wealth_players.forEach((player, index) => {
                        const raceClass = player.race.toLowerCase();
                        const rankIcon = index === 0 ? '💰' : index === 1 ? '💎' : index === 2 ? '🏆' : '💸';
                        html += `
                            <div class="epic-player" style="animation-delay: ${index * 0.1 + 1}s; font-size: 0.9rem;">
                                <div class="d-flex align-items-center">
                                    <div class="player-rank" style="font-size: 1.2rem;">${rankIcon} #${index + 1}</div>
                                    <div class="flex-grow-1">
                                        <div class="player-name-epic ${raceClass}" style="font-size: 1rem;">${player.name}</div>
                                        <div class="player-stats" style="font-size: 0.8rem;">${player.wealth} kinah</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    topWealthContainer.innerHTML = html;
                }

                // Update top guilds
                const topGuildsContainer = document.getElementById('top-guilds');
                topGuildsContainer.innerHTML = '';

                if (!stats.top_guilds || stats.top_guilds.length === 0) {
                    topGuildsContainer.innerHTML = '<div class="text-center text-muted">No guild data available</div>';
                } else {
                    let html = '';
                    stats.top_guilds.forEach((guild, index) => {
                        const rankIcon = index === 0 ? '👑' : index === 1 ? '🛡️' : index === 2 ? '⚔️' : '🏰';
                        html += `
                            <div class="epic-player" style="animation-delay: ${index * 0.1 + 1.5}s; font-size: 0.9rem;">
                                <div class="d-flex align-items-center">
                                    <div class="player-rank" style="font-size: 1.2rem;">${rankIcon} #${index + 1}</div>
                                    <div class="flex-grow-1">
                                        <div class="player-name-epic" style="font-size: 1rem; color: var(--aion-gold);">${guild.name}</div>
                                        <div class="player-stats" style="font-size: 0.8rem;">${guild.members} members • Lv.${guild.level}</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    topGuildsContainer.innerHTML = html;
                }

                // Update most active players
                const topActiveContainer = document.getElementById('top-active-players');
                if (topActiveContainer) {
                    topActiveContainer.innerHTML = '';

                    if (!stats.top_active_players || stats.top_active_players.length === 0) {
                        topActiveContainer.innerHTML = '<div class="text-center text-muted">No activity data available</div>';
                    } else {
                        let html = '';
                        stats.top_active_players.forEach((player, index) => {
                            const raceClass = (player.race || '').toLowerCase();
                            const rankIcon = index === 0 ? '🔥' : index === 1 ? '⚡' : index === 2 ? '💪' : '🌟';
                            html += `
                                <div class="epic-player" style="animation-delay: ${index * 0.1 + 2}s; font-size: 0.9rem;">
                                    <div class="d-flex align-items-center">
                                        <div class="player-rank" style="font-size: 1.2rem;">${rankIcon} #${index + 1}</div>
                                        <div class="flex-grow-1">
                                            <div class="player-name-epic ${raceClass}" style="font-size: 1rem;">${player.name}</div>
                                            <div class="player-stats" style="font-size: 0.8rem;">${player.activity}</div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        topActiveContainer.innerHTML = html;
                    }
                }

                // Update top abyss points players
                const topAbyssContainer = document.getElementById('top-abyss-players');
                if (topAbyssContainer) {
                    topAbyssContainer.innerHTML = '';

                    if (!stats.top_abyss_players || stats.top_abyss_players.length === 0) {
                        topAbyssContainer.innerHTML = '<div class="text-center text-muted">No abyss data available</div>';
                    } else {
                        let html = '';
                        stats.top_abyss_players.forEach((player, index) => {
                            const raceClass = player.race.toLowerCase();
                            const rankIcon = index === 0 ? '👁️' : index === 1 ? '🌀' : index === 2 ? '⚡' : '💫';
                            html += `
                                <div class="epic-player" style="animation-delay: ${index * 0.1 + 2.5}s; font-size: 0.9rem;">
                                    <div class="d-flex align-items-center">
                                        <div class="player-rank" style="font-size: 1.2rem;">${rankIcon} #${index + 1}</div>
                                        <div class="flex-grow-1">
                                            <div class="player-name-epic ${raceClass}" style="font-size: 1rem;">${player.name}</div>
                                            <div class="player-stats" style="font-size: 0.8rem;">${player.abyss_points} AP • ${player.class}</div>
                                        </div>
                                        <span class="race-badge ${raceClass}">${player.race}</span>
                                    </div>
                                </div>
                            `;
                        });
                        topAbyssContainer.innerHTML = html;
                    }
                }

                // Update top glory points players
                const topGPContainer = document.getElementById('top-gp-players');
                if (topGPContainer) {
                    topGPContainer.innerHTML = '';

                    if (!stats.top_gp_players || stats.top_gp_players.length === 0) {
                        topGPContainer.innerHTML = '<div class="text-center text-muted">No glory data available</div>';
                    } else {
                        let html = '';
                        stats.top_gp_players.forEach((player, index) => {
                            const raceClass = player.race.toLowerCase();
                            const rankIcon = index === 0 ? '⭐' : index === 1 ? '🌟' : index === 2 ? '✨' : '💎';
                            html += `
                                <div class="epic-player" style="animation-delay: ${index * 0.1 + 3}s; font-size: 0.9rem;">
                                    <div class="d-flex align-items-center">
                                        <div class="player-rank" style="font-size: 1.2rem;">${rankIcon} #${index + 1}</div>
                                        <div class="flex-grow-1">
                                            <div class="player-name-epic ${raceClass}" style="font-size: 1rem;">${player.name}</div>
                                            <div class="player-stats" style="font-size: 0.8rem;">${player.glory_points} GP • ${player.class}</div>
                                        </div>
                                        <span class="race-badge ${raceClass}">${player.race}</span>
                                    </div>
                                </div>
                            `;
                        });
                        topGPContainer.innerHTML = html;
                    }
                }

            } catch (error) {
                console.error('Error loading rankings:', error);
                // Gracefully handle missing sections
                const failMap = {
                    'top-level-players': 'Failed to load rankings',
                    'top-pvp-players': 'Failed to load rankings',
                    'top-wealth-players': 'Failed to load wealth data',
                    'top-guilds': 'Failed to load guild data',
                    'top-active-players': 'Failed to load activity data'
                };
                Object.entries(failMap).forEach(([id, msg]) => {
                    const el = document.getElementById(id);
                    if (el) el.innerHTML = `<div class="text-center text-muted">${msg}</div>`;
                });
                const topAbyssContainer = document.getElementById('top-abyss-players');
                const topGPContainer = document.getElementById('top-gp-players');
                if (topAbyssContainer) topAbyssContainer.innerHTML = '<div class="text-center text-muted">Failed to load abyss data</div>';
                if (topGPContainer) topGPContainer.innerHTML = '<div class="text-center text-muted">Failed to load glory data</div>';
            }
        }

        // Load rankings when page loads
        document.addEventListener('DOMContentLoaded', loadRankings);

        // Refresh rankings every 30 seconds
        setInterval(loadRankings, 30000);
    </script>
</body>
</html>
