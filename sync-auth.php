<?php
require_once __DIR__.'/app/database.php';
require_once __DIR__.'/app/functions.php';
header('Content-Type: text/html; charset=utf-8');

$account = isset($_GET['account']) ? trim($_GET['account']) : '';
if($account===''){
    echo '<h3>Sync login account to authuser</h3>';
    echo '<p>Provide ?account=USERNAME to copy/update credentials from user_auth into authuser (if that table exists).</p>';
    exit;
}

try{
    $db = db_ls();
    if($db->offline) throw new Exception('Login DB offline.');

    // helpers
    $getCols = function($table) use ($db){
        try { return $db->queryFetch("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$table]) ?: []; }
        catch(Exception $e){ return []; }
    };
    $colExists = function($table,$col) use ($getCols){ $set=[]; foreach($getCols($table) as $c){ $set[strtolower($c['COLUMN_NAME'])]=true; } return isset($set[strtolower($col)]); };
    $pickCol = function($table, $cands) use ($colExists){ foreach($cands as $c){ if($colExists($table,$c)) return $c; } return null; };
    $hasTable = function($table) use ($db){ try{ return (bool)$db->queryFetchSingle("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$table]); }catch(Exception $e){ return false; } };

    if(!$hasTable('authuser')){ echo '<p>authuser table not found. Nothing to sync.</p>'; exit; }

    // read from user_auth
    $ua = $db->queryFetchSingle("SELECT account, CONVERT(VARCHAR(64), password, 1) AS pwd_hex FROM dbo.user_auth WHERE LOWER(account)=LOWER(?)", [$account]);
    if(!$ua) throw new Exception('Account not found in user_auth.');

    $acctCol = $pickCol('authuser',['account','name']);
    $pwdCol  = $pickCol('authuser',['pwd','password']);
    if(!$acctCol || !$pwdCol) throw new Exception('authuser schema missing account/pwd columns.');

    $exists = $db->queryFetchSingle("SELECT 1 FROM dbo.authuser WHERE LOWER($acctCol)=LOWER(?)", [$ua['account']]);

    if(!$exists){
        $optCols = [];$optVals = [];$optQ = '';
        foreach(['otpflag'=>0,'otp_flag'=>0,'pwd_flag'=>0,'activated'=>1,'status'=>0,'access_level'=>0,'membership'=>0] as $c=>$v){
            if($colExists('authuser',$c)){ $optCols[]=$c; $optVals[]=$v; }
        }
        $cols = "$acctCol,$pwdCol" . (count($optCols)? ",".implode(',', $optCols):'');
        $vals = "?, CONVERT(VARBINARY(16), ?, 1)" . (count($optCols)? ",".implode(',', array_fill(0,count($optCols),'?')):'');
        $params = array_merge([$ua['account'], $ua['pwd_hex']], $optVals);
        $ok = $db->query("INSERT INTO dbo.authuser ($cols) VALUES ($vals)", $params);
        echo $ok ? '<p style="color:green">Inserted into authuser.</p>' : '<p style="color:red">Insert failed.</p>';
    } else {
        $ok = $db->query("UPDATE dbo.authuser SET $pwdCol = CONVERT(VARBINARY(16), ?, 1) WHERE LOWER($acctCol)=LOWER(?)", [$ua['pwd_hex'], $ua['account']]);
        echo $ok ? '<p style="color:green">Updated authuser password.</p>' : '<p style="color:red">Update failed.</p>';
    }

    echo '<p>Done for account: <b>'.htmlspecialchars($ua['account'])."</b></p>";
    echo '<p><a href="auth-diagnostic.php?account='.urlencode($ua['account']).'">Back to diagnostic</a></p>';

}catch(Exception $e){
    echo '<p style="color:red">Error: '.htmlspecialchars($e->getMessage()).'</p>';
}

