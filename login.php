<?php
require_once __DIR__.'/app/auth.php';
start_session();

$message = '';
if($_SERVER['REQUEST_METHOD']==='POST'){
    try{
        // Log the login attempt for debugging
        $username = $_POST['username'] ?? '';
        $logEntry = date('c') . " Login attempt for user: " . $username . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "\n";
        @file_put_contents(__DIR__.'/app/login_debug.log', $logEntry, FILE_APPEND);

        AuthService::login($username, $_POST['password'] ?? '');

        // Log successful login
        $logEntry = date('c') . " Login successful for user: " . $username . "\n";
        @file_put_contents(__DIR__.'/app/login_debug.log', $logEntry, FILE_APPEND);

        header('Location: usercp.php');
        exit;
    }catch(Exception $e){
        $message = $e->getMessage();

        // Log failed login
        $logEntry = date('c') . " <PERSON>gin failed for user: " . ($username ?? 'unknown') . " - Error: " . $e->getMessage() . "\n";
        @file_put_contents(__DIR__.'/app/login_debug.log', $logEntry, FILE_APPEND);
    }
}
?>
<!doctype html><html><head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>Login - Elyon Aion</title>
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/styles.css">
</head><body>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-5">
            <h3 class="mb-3">Login</h3>
            <?php if($message): ?><div class="alert alert-danger"><?php echo htmlspecialchars($message); ?></div><?php endif; ?>
            <form method="post">
                <div class="mb-3"><label class="form-label">Username</label><input class="form-control" name="username" required></div>
                <div class="mb-3"><label class="form-label">Password</label><input class="form-control" type="password" name="password" required></div>
                <button class="btn btn-primary">Login</button>
                <a class="btn btn-link" href="register.php">Create account</a>
            </form>
        </div>
    </div>
</div>
</body></html>

