<?php
require_once __DIR__.'/app/functions.php';
header('Content-Type: application/json');

try {
    $db = db_web();
    if ($db->offline) throw new Exception('web db offline');

    // Try weekly packages first, then fallback to regular shop items
    $items = $db->queryFetch("SELECT TOP 8 id, package_name AS name, package_cost AS cost, package_img AS img, NULL AS item_id FROM website_package_shop ORDER BY id DESC");
    if (!is_array($items) || count($items) === 0) {
        $items = $db->queryFetch("SELECT TOP 8 item_id, name, cost, img FROM website_shop_items WHERE status = 1 ORDER BY id DESC");
        if (is_array($items)) {
            $items = array_map(function($r){
                return [
                    'item_id' => $r['item_id'],
                    'name' => $r['name'],
                    'cost' => (int)$r['cost'],
                    'img' => (isset($r['img']) && $r['img'] ? (dirname($_SERVER['SCRIPT_NAME']).'/static/images/shop/'.$r['img']) : (dirname($_SERVER['SCRIPT_NAME']).'/static/images/shop/default.gif'))
                ];
            }, $items);
        }
    } else {
        // normalize weekly packages
        $items = array_map(function($r){
            return [
                'item_id' => $r['id'],
                'name' => $r['name'],
                'cost' => (int)$r['cost'],
                'img' => (isset($r['img']) && $r['img'] ? (dirname($_SERVER['SCRIPT_NAME']).'/static/images/shop/'.$r['img']) : (dirname($_SERVER['SCRIPT_NAME']).'/static/images/shop/default.gif'))
            ];
        }, $items);
    }

    echo json_encode(['items' => $items ?: []]);
} catch (Exception $e) {
    http_response_code(200);
    echo json_encode(['items' => [], 'error' => $e->getMessage()]);
}

