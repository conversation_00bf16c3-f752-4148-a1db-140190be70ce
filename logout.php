<?php
require_once __DIR__.'/app/functions.php';

// Log the logout attempt for debugging
start_session();
$username = $_SESSION['username'] ?? 'unknown';
$logEntry = date('c') . " Logout for user: " . $username . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "\n";
@file_put_contents(__DIR__.'/app/login_debug.log', $logEntry, FILE_APPEND);

logoutSession();

// Log successful logout
$logEntry = date('c') . " Logout completed for user: " . $username . "\n";
@file_put_contents(__DIR__.'/app/login_debug.log', $logEntry, FILE_APPEND);

// Go back to home page after logout
header('Location: index.php');
exit;

