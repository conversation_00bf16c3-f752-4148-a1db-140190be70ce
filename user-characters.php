<?php
require_once __DIR__.'/app/functions.php';
header('Content-Type: application/json');
try{
  start_session();
  if(!isLoggedIn()) { echo json_encode([]); exit; }
  $db = db_gs();
  if ($db->offline) { echo json_encode([]); exit; }
  // Try to find account link either by uid or by account name via web_account
  $uid = $_SESSION['uid'];
  $acc = $_SESSION['username'];
  // Prefer account_id field when present
  $rows = $db->queryFetch("SELECT TOP 8 user_id, level, race, player_class FROM user_data WHERE account_id = ? ORDER BY level DESC", [$uid]);
  if(!is_array($rows) || count($rows)===0){
    $rows = $db->queryFetch("SELECT TOP 8 user_id, level, race, player_class FROM user_data WHERE account_name = ? ORDER BY level DESC", [$acc]);
  }
  echo json_encode($rows ?: []);
}catch(Exception $e){ echo json_encode([]); }

