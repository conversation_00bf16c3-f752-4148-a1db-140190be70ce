<?php
require_once __DIR__.'/app/auth.php';
start_session();

$message = '';$success=false;
if($_SERVER['REQUEST_METHOD']==='POST'){
    try{
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        // Register the account
        AuthService::register($username, $_POST['email'] ?? '', $password);

        // Automatically log in the new user
        AuthService::login($username, $password);

        // Redirect to dashboard
        header('Location: usercp.php');
        exit;

    }catch(Exception $e){
        $message = $e->getMessage();
    }
}
?>
<!doctype html><html><head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>Register - Elyon Aion</title>
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/styles.css">
</head><body>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <h3 class="mb-3">Create Account</h3>
            <?php if($message): ?><div class="alert alert-danger"><?php echo htmlspecialchars($message); ?></div><?php endif; ?>
            <?php if($success): ?><div class="alert alert-success">Account created! You can now <a href="login.php">log in</a>.</div><?php endif; ?>
            <form method="post">
                <div class="mb-3"><label class="form-label">Username</label><input class="form-control" name="username" maxlength="25" required></div>
                <div class="mb-3"><label class="form-label">Email</label><input class="form-control" type="email" name="email" required></div>
                <div class="mb-3"><label class="form-label">Password</label><input class="form-control" type="password" name="password" required>
                    <small class="text-muted">6-15 characters, letters and numbers only.</small>
                </div>
                <button class="btn btn-success">Register</button>
                <a class="btn btn-link" href="login.php">I have an account</a>
            </form>
        </div>
    </div>
</div>
</body></html>

